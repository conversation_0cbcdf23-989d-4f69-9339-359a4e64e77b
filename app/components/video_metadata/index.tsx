// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, I18nManager } from 'react-native';
import { Audio } from 'expo-av';
import { VideoCameraIcon } from 'react-native-heroicons/outline';

import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { changeOpacity } from '@utils/theme';
import { formatVideoDurationFromMillis } from '@utils/video_duration';
import { isVideo } from '@utils/file';
import MediaMessageMetadata from '@components/media_message_metadata';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';

type VideoMetadataProps = {
    files: FileInfo[];
    isCurrentUser?: boolean;
    asOverlay?: boolean; // New prop to determine if this should render as overlay
    // New props for timestamp and read receipts
    post?: PostModel;
    currentUser?: UserModel;
    acknowledgementsVisible?: boolean;
    hasReactions?: boolean;
    location?: string;
    showTimestamp?: boolean;
    showReadReceipts?: boolean;
};

const VideoMetadata: React.FC<VideoMetadataProps> = ({
    files,
    isCurrentUser = false,
    asOverlay = false,
    post,
    currentUser,
    acknowledgementsVisible = false,
    hasReactions = false,
    location = '',
    showTimestamp = true,
    showReadReceipts = true,
}) => {
    const theme = useTheme();
    const serverUrl = useServerUrl();
    const [videoDuration, setVideoDuration] = useState<number | null>(null);
    const mounted = useRef(false);
    const isRTL = I18nManager.isRTL;

    // Find the first video file
    const videoFile = files.find(file => isVideo(file));

    useEffect(() => {
        mounted.current = true;
        return () => {
            mounted.current = false;
        };
    }, []);

    useEffect(() => {
        const getVideoDuration = async () => {
            if (!videoFile?.id) return;

            try {
                const videoUrl = `${serverUrl}/api/v4/files/${videoFile.id}`;
                const { sound } = await Audio.Sound.createAsync(
                    { uri: videoUrl },
                    { shouldPlay: false }
                );
                const status = await sound.getStatusAsync();
                if (status.isLoaded && status.durationMillis) {
                    if (mounted.current) {
                        setVideoDuration(status.durationMillis);
                    }
                }
                await sound.unloadAsync();
            } catch (error) {
                console.log('Could not extract video duration:', error);
            }
        };

        if (videoFile) {
            getVideoDuration();
        }
    }, [videoFile, serverUrl]);

    // Don't render if no video file
    if (!videoFile) {
        return null;
    }

    // Overlay styles for positioning on video content
    const overlayStyles = StyleSheet.create({
        container: {
            position: 'absolute',
            bottom: 8,
            left: 0,
            right: 0,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 12,
            zIndex: 10,
        },
        cameraIconContainer: {
            backgroundColor: changeOpacity('black', 0.6),
            borderRadius: 12,
            padding: 4,
            alignItems: 'center',
            justifyContent: 'center',
        },
        durationContainer: {
            backgroundColor: changeOpacity('black', 0.6),
            borderRadius: 8,
            paddingHorizontal: 6,
            paddingVertical: 2,
        },
        durationText: {
            color: 'white',
            fontSize: 12,
            fontFamily: 'IBMPlexSansArabic-Medium',
            textAlign: 'center',
        },
    });

    // Footer styles for legacy footer positioning
    const footerStyles = {
        container: {
            flexDirection: 'row' as const,
            alignItems: 'center' as const,
            marginRight: 8,
        },
        cameraIconContainer: {
            flexDirection: 'row' as const,
            alignItems: 'center' as const,
            right: 150,
            marginRight: videoDuration ? 6 : 0,
        },
        durationContainer: {
            marginRight: -100,
            right: 190,
        },
        durationText: {
            color: 'white',
            fontSize: 14,
            fontFamily: 'IBMPlexSansArabic-Medium',
            textAlign: 'center' as const,
            lineHeight: 20,
        },
    };

    const styles = asOverlay ? overlayStyles : footerStyles;

    if (asOverlay) {
        return (
            <View style={styles.container}>
                {/* Camera Icon - Bottom Left */}
                <View style={styles.cameraIconContainer}>
                    <VideoCameraIcon
                        size={16}
                        fill="white"
                    />
                </View>

                {/* Duration and Metadata Container - Bottom Right */}
                <View style={{
                    flexDirection: isRTL ? "row-reverse" : "row",
                    alignItems: "center",
                    gap: 8,
                }}>
                    {/* Duration Display */}
                    {videoDuration && (
                        <View style={styles.durationContainer}>
                            <Text style={styles.durationText}>
                                {formatVideoDurationFromMillis(videoDuration)}
                            </Text>
                        </View>
                    )}

                    {/* Timestamp and Read Receipts */}
                    {post && (
                        <MediaMessageMetadata
                            post={post}
                            currentUser={currentUser}
                            isCurrentUser={isCurrentUser}
                            acknowledgementsVisible={acknowledgementsVisible}
                            hasReactions={hasReactions}
                            location={location}
                            showTimestamp={showTimestamp}
                            showReadReceipts={showReadReceipts}
                            textColor="white"
                            iconColor="white"
                            iconSize={12}
                            containerStyle={{
                                backgroundColor: changeOpacity('black', 0.6),
                                borderRadius: 8,
                                paddingHorizontal: 6,
                                paddingVertical: 2,
                            }}
                        />
                    )}
                </View>
            </View>
        );
    }

    // Legacy footer mode
    return (
        <View style={styles.container}>
            {/* Video Camera Icon */}
            <View style={styles.cameraIconContainer}>
                <VideoCameraIcon
                    size={25}
                    fill="white"
                />
            </View>

            {/* Duration Display */}
            {videoDuration && (
                <View style={styles.durationContainer}>
                    <Text style={styles.durationText}>
                        {formatVideoDurationFromMillis(videoDuration)}
                    </Text>
                </View>
            )}
        </View>
    );
};

export default VideoMetadata;
