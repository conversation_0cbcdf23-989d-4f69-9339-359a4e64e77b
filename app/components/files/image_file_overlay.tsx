// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useMemo} from 'react';
import {PixelRatio, StyleSheet, Text, useWindowDimensions, View} from 'react-native';

import {useTheme} from '@context/theme';
import {useIsTablet} from '@hooks/device';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

type ImageFileOverlayProps = {
    value: number;
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    moreImagesWrapper: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
     backgroundColor:changeOpacity(theme.centerChannelBg,100),
     opacity:0.4,
     blurRadius:1,
      
        borderRadius: 5,
    },
    moreImagesText: {
        color: 'white',
        fontFamily: 'IBMPlexSansArabic',
        textAlign: 'center',
    },
}));

const ImageFileOverlay = ({value}: ImageFileOverlayProps) => {
    const dimensions = useWindowDimensions();
    const isTablet = useIsTablet();
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const textStyles = useMemo(() => {
        const scale = isTablet ? dimensions.scale : 1;
        return [
            style.moreImagesText,
            {fontSize: Math.round(PixelRatio.roundToNearestPixel(24 * scale))},
        ];
    }, [isTablet]);

    return (
        <View style={style.moreImagesWrapper}>
            <Text style={textStyles}>
                {`+${value}`}
            </Text>
        </View>
    );
};

export default ImageFileOverlay;
